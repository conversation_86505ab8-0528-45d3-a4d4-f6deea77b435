import asyncio
import logging
import random
import time
from enum import Enum
from typing import Dict, List, Literal

import aiohttp
from fastmcp import FastMCP
from pydantic import BaseModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

mcp = FastMCP("Fake WRC&WAIC industrial operations MCP Server")

class RoleType(str, Enum):
    tianyi = "tianyi"
    ur = "ur"
    franka = "franka"
    electrician= "electrician"

dst_map = {
    "franka_workstation": [0.1, 0.1, 90.0],
    "ur_workstation": [0.2, 0.2, 45.0],
    "audience_area": [0.3, 0.3, 180.0],
    "shelf": [0.4, 0.4, 0.0],
}

sleep_time = 5

async def _async_post(url: str, json_data: dict):
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=json_data) as resp:
                # response_text = await resp.text()
                return
    except Exception as e:
        print(f"发送失败: {e}")
        return


@mcp.tool()
async def start_for_electrician():
    """Start the Tianyi4 workstation to inspect and repair electrical control cabinets.

    Returns:
        Dict of result message, {'state': 'succeed' or 'failed', 'msg': 'failed reason or empty'}.
    """
    await asyncio.sleep(sleep_time)
    logger.info("tianyi4 workstation start inspect and repair electrical control cabinets task finished.")
    return {"state": "succeed", "msg": ""}


@mcp.tool()
async def continue_inspect() -> Dict[str, str]:
    """Tianyi4 workstation continue to inspect and repair electrical control cabinets.

    Returns:
        Dict of result message, {'state': 'succeed' or 'failed', 'msg': 'failed reason or empty'}.
    """
    await asyncio.sleep(sleep_time) 
    return {"state": "succeed", "msg": ""}


@mcp.tool()
async def start_for_franka():
    """Start the dual-arm Franka workstation to pack gift bags into boxes.

    Returns:
        Dict of result message, {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.
    """
    await asyncio.sleep(sleep_time)
    logger.info("Franka workstation task finished.")
    return {"state": "succeed", "msg": ""}


@mcp.tool()
async def pick_and_insert_bulb():
    """UR robot picks a light bulb from the box, and insert it onto the test device, until there is no light bulb left in the box.
           
    Returns:    
        Dict of result message, e.g. {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.    
    """
    await asyncio.sleep(sleep_time)
    return {"state": "succeed", "msg": ""}


@mcp.tool()
async def pull_and_place_bulb(inspect_result: List[int]):
    """UR robot pulls a light bulb from the test device and place it into the box until there is no bulb left on the device. 
           
    Returns:    
        Dict of result message, e.g. {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.    
    """
    await asyncio.sleep(sleep_time)
    return {"state": "succeed", "msg": ""}


@mcp.tool()
async def inspect_bulb() -> Dict[str, str]:
    """UR robot turn on the power switch of the test device and inspect if any of the light bulb is malfunctioning.

    Returns:    
        Dict of result message, e.g. {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.
        if "state" is "succeed", a list representing the status of a light bulb in its slot in the test deviceis
        returned, in which,
            1: good light bulb
            0: malfunctioned light bulb
            -1: no light bulb is inserted on the slot
    """
    await asyncio.sleep(sleep_time)
    random_array = [random.choice([0, 1, -1]) for _ in range(6)]
    return {"state": "succeed", "msg": f"{random_array}"}


@mcp.tool()
def ur_to_shelves() -> Dict[str, str]:
    """Carry the box from the front table to shelves.

    Returns:
        Dict of result message, {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.
    """
    logger.info("Box carrying task finished.")
    return {"state": "succeed", "msg": ""}


@mcp.tool()
def shelves_to_ur() -> Dict[str, str]:
    """Carry the box from the shelves to the front table.

    Returns:
        Dict of result message, {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.
    """
    logger.info("Box carrying task finished.")
    return {"state": "succeed", "msg": ""}


@mcp.tool()
def put_to_gift_table() -> Dict[str, str]:
    """Put the box on the front table for gifts.

    Returns:
        Dict of result message, {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.
    """
    logger.info("Box putting task finished.")
    return {"state": "succeed", "msg": ""}


@mcp.tool()
async def sort_bulb(flag: Literal["start", "pause"]) -> Dict[str, str]:
    """Pick out light bulbs from industrial parts and put them into boxes. Flag determines whether to start or pause the sorting task
    
    Args:
        flag: one of [start, pause], means start or pause sort bulbs.

    Returns:
        Dict of result message, {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.
    """
    time.sleep(sleep_time) 
    logger.info("Light bulbs sorting task finished.")
    return {"state": "succeed", "msg": ""}


if __name__ == "__main__":
    # 启动方式根据你的需求调整
    # asyncio.run(call(Role(name=RoleType.tianyi), "开始"))
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000, log_level="DEBUG")