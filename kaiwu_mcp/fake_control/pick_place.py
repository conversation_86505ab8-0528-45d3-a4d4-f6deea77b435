import random
from fastmcp import FastMCP


mcp = FastMCP("Fake Pick Place MCP Server")


@mcp.tool()
def fake_pick_place(obj: str, location: str) -> dict:
    """Pick up an object and place it to somewhere.
    
    Args:
        obj: Object to pick up.
        location: Where to place.
        
    Returns:
        Dict of result message, {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.
    """
    return random.choice([
        {"state": "succeed", "msg": ""},
        {"state": "failed", "msg": f"can not find object: {obj}"},
        {"state": "failed", "msg": f"can not find location: {location}"},
    ])


if __name__ == "__main__":
    # 只能本地访问用stdio，远程访问用sse或streamable-http
    # host如果设置为127.0.0.1则只能被本地访问，设置为 0.0.0.0 才能被远程访问
    # mcp.run(transport="stdio")  # Default, so transport argument is optional
    # mcp.run(transport="streamable-http", host="0.0.0.0", port=8000, path="/mcp")
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000, log_level="DEBUG")
