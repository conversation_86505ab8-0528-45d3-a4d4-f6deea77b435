import random
from fastmcp import FastMCP


mcp = FastMCP("Fake Table Cleaning MCP Server")


@mcp.tool()
def table_cleaning(flag: str) -> dict:
    """Cleaning table according to instruction `flag`.
    
    Args:
        flag: one of [start, pause, stop], means start or pause or stop cleaning.
        
    Returns:
        Dict of result message, {'state': 'succeed or failed', 'msg': 'failed reason or empty'}.
    """
    return random.choice([
        {"state": "succeed", "msg": ""},
        {"state": "failed", "msg": f"can not {flag} table cleaning"},
    ])


if __name__ == "__main__":
    # 只能本地访问用stdio，远程访问用sse或streamable-http
    # host如果设置为127.0.0.1则只能被本地访问，设置为 0.0.0.0 才能被远程访问
    # mcp.run(transport="stdio")  # Default, so transport argument is optional
    # mcp.run(transport="streamable-http", host="0.0.0.0", port=8000, path="/mcp")
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000, log_level="DEBUG")
