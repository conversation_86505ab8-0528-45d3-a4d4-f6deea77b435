import random
from fastmcp import FastMCP
from typing import List
import time
import requests

mcp = FastMCP("Light bulb inspection MCP Server")


@mcp.tool()
def serve_light_bulb_box():
    """从货架搬箱子到UR工作站
    """
    time.sleep(random.randint(1, 5))
    return random.choice(["正确执行"])


@mcp.tool()
def retrieve_bulb_box():
    """从UR工作站搬箱子到货架
    """
    time.sleep(random.randint(1, 5))
    return random.choice(["Succeed"])


@mcp.tool()
def pick_and_insert_bulb():
    """将灯泡从箱子里拿出来, 放置到灯泡座上
    """
    time.sleep(random.randint(1, 5))
    return random.choice(["Succeed"])


@mcp.tool()
def pull_and_place_bulb(inspect_result: List[int]):
    """从灯泡座上拔出灯泡，根据检测结果(inspect_result)将灯泡放置在不同的箱子里

    Args:
        inspect_result (List[int]): 灯泡检测结果

    Returns:
        str: 执行结果
    """
    time.sleep(random.randint(1, 5))
    return random.choice(["Succeed"])


@mcp.tool()
def inspect_lightbulb() -> List[int]:
    """灯泡质量检测工具, 0表示无故障, 1表示有故障
    """
    time.sleep(random.randint(1, 5))
    return [0, 0, 0, 0, 1]


if __name__ == "__main__":
    # Initialize and run the server
    # 只能本地访问用stdio，远程访问用sse
    # mcp.run(transport='stdio')
    # mcp.run(transport='sse')
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000, log_level="DEBUG")
