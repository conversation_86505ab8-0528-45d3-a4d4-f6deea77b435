# 必须用python3.10及以上
venv:
	python3 -c 'import sys; exit(not (sys.version_info >= (3, 10)))' || { echo "Python version must be >= 3.10, run `make py312` then `make ven312` instead"; exit 1; }
	python3 -m venv .venv

# 安装 python3.12
py312:
	bash scripts/install_py312.sh

nodejs:
	curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
	sudo apt-get install -y nodejs

uv:
	curl -LsSf https://astral.sh/uv/install.sh | sh

venv312:
	python3.12 -m venv .venv --system-site-packages

# Install all dependencies
dev:
	make deps

deps:
	uv pip install --index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple -r ./requirements.txt
	
# Remove common intermediate files.
clean:
	find . -name '*.pyc' -print0 | xargs -0 rm -f
	find . -name '*.swp' -print0 | xargs -0 rm -f
	find . -name '.DS_Store' -print0 | xargs -0 rm -rf
	find . -name '__pycache__' -print0 | xargs -0 rm -rf
	-rm -rf \
		*.egg-info \
		.coverage* \
		.eggs \
		.mypy_cache \
		.pytest_cache \
		PYTEST_TMPDIR \
		relative_cache \
		Pipfile* \
		build \
		dist \
		output \
		public

# Remove common intermediate files alongside with `pre-commit` hook.
deepclean: clean
	pre-commit uninstall --hook-type pre-push

# CICD environment check etc.
cicd-info:
	env | sort
	# nvidia-smi
	which python
	which pip
	python --version
	python -m site
	which python3
	which pip3
	python3 --version
	python3 -m site
	pwd
	ls -la
