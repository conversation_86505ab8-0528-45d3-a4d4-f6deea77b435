# kaiwu_mcp
开物具身智能mcp广场，维护机器人相关的各种mcp服务，包括机器人操作技能、人脸识别等各种工具，给kaiwu_agent调用
- 基于[fastmcp](https://github.com/jlowin/fastmcp/tree/main)框架构建mco server，The fast, Pythonic way to build MCP servers and clients
- [mcp协议介绍](https://modelcontextprotocol.io/introduction)
- [inspector](https://github.com/modelcontextprotocol/inspector)：连接mcp sevrer并测试

## change log
* [2025.05.26] 基于inspector工具连接pick place server并测试成功

## install
```
# 安装python环境管理器 uv （mcp相关推荐uv，尽量不用conda）
make uv
# 把下面这个指令写入`~/.bashrc`，否则找不到uv
export PATH=/home/<USER>/.local/bin:$PATH

# 创建并激活python虚拟环境，uv会提供python3.13等
uv venv
source .venv/bin/activate

# 安装nodejs，用于inspector调试mcp server
make nodejs

# 安装依赖
make deps 
```

## create mcp server
参考示例[fake_pick_place](kaiwu_mcp/fake_control/pick_place.py)，注意：
- 每个tool的文档都要写，包括doc string (函数作用说明、Args、Returns)以及形参描述（什么类型），这些输入大模型prompt，写不好不清楚则大模型调用失败
- host如果设置为127.0.0.1，则只能被本地访问，设置为0.0.0.0才能被远程访问

## run mcp server
1. 推荐用sse方式启动服务端，并用inspector通过sse测试
```
# 启动mcp server
## （推荐）启动指定server
## 注意，如果pick_place.py里transport设置为stdio则只能在本地访问，需要被远程访问改成sse或streamble-http
python3 kaiwu_mcp/fake_control/pick_place.py

## 或启动所有server
python3 server.py

# （可选）启动inspector用于测试
## 打开 http://127.0.0.1:6274
## Transport选sse: connect，list tools，传入参数开始测试
fastmcp dev kaiwu_mcp/fake_control/pick_place.py
```

2. 或者用stdio方式启动服务端（远程无法访问），并用inspector通过stdio测试
```
# 启动inspector
fastmcp dev kaiwu_mcp/fake_control/pick_place.py

# 打开inspector http://127.0.0.1:6274
## Transport选stdio，以下两种方式都可以启动mcp server，
## 注意不需要手动启动服务端，inspector会用你提供的command和args帮你启动
### Command填`fastmcp`, Args填`run kaiwu_mcp/fake_control/pick_place.py`
### Command填`uv`, Args填`run --with fastmcp fastmcp run kaiwu_mcp/fake_control/pick_place.py`
```

## how to use
参考[kaiwu_agent](http://**********/kaiwu/agent/kaiwu_agent/-/blob/dev/kaiwu_agent/configs/commons.py?ref_type=heads)中的用法，配置mcp server信息，就可以调用tools：
```
"mcp_servers": { 
    "fake_pick_place": {
        # 服务地址
        "url": "http://server_ip:server_port/sse",
        "transport": "sse",
    },
}
```

## todo
