##############################
### Virtual ENV
.venv

### CI/CD Files

# Root level file used in CI to specify certain env configs.
env


##############################
### Build Staff

# Build pypl
*.egg-info/
*.egg-link
*.egg*
build

##############################
### Editor staff

# Visual Studio Code files
.vs
.vscode/launch.json
# /.vscode/*
!/.vscode/extensions.json
!/.vscode/settings_recommended.json

# PyCharm files
.idea

# IPython notebook checkpoints
.ipynb_checkpoints

# Eclipse Project settings
*.*project
.settings

# NFS handle files
**/.nfs*

# Sublime Text settings
*.sublime-workspace
*.sublime-project

# QtCreator files
*.user

# GDB history
.gdb_history

# Files generated by CLion
cmake-build-debug

# Editor temporaries
*.swn
*.swo
*.swp
*~

##############################
### Compiled staff

# Compiled python
*.pyc
*.pyd
__pycache__

# Compiled Object files
*.slo
*.lo
*.o
*.cuo
*.obj

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Compiled protocol buffers
*.pb.h
*.pb.cc
*_pb2.py

# Compiled MATLAB
*.mex*

##############################
### General Files

# zip archives
*.zip

# video files
*.mp4
*.avi

# core dump files
core.*

# coverage files
.coverage*
*/**/.coverage.*

# macOS dir files
.DS_Store

# Ninja files
.ninja_deps
.ninja_log
compile_commands.json

# test files
*work_dirs*

# output
outputs

##############################
# BEGIN NOT-CLEAN-FILES (setup.py handles this marker. Do not change.)
#
# Below files are not deleted by "setup.py clean".
assets
.vscode/
