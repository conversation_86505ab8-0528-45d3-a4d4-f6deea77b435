#!/bin/bash

set -e  # 出错即退出

PY_VERSION=3.12.2
PY_SHORT=3.12
PY_SRC_DIR="/usr/src/Python-$PY_VERSION"
PY_TGZ="Python-$PY_VERSION.tgz"
PY_BIN="/usr/local/bin/python$PY_SHORT"

echo "🐍 开始安装 Python $PY_VERSION + pip + venv..."

# 安装依赖
echo "🔧 安装构建依赖..."
sudo apt update
sudo apt install -y build-essential \
  zlib1g-dev libncurses5-dev libgdbm-dev libnss3-dev libssl-dev \
  libreadline-dev libffi-dev libsqlite3-dev wget curl libbz2-dev \
  liblzma-dev uuid-dev tk-dev libgdbm-compat-dev

# 清理旧源码
if [ -d "$PY_SRC_DIR" ]; then
  echo "🧹 删除旧源码目录..."
  sudo rm -rf "$PY_SRC_DIR"
fi

cd /usr/src
echo "📥 下载 Python $PY_VERSION..."
sudo wget -c "https://www.python.org/ftp/python/$PY_VERSION/$PY_TGZ"
sudo tar -xf "$PY_TGZ"
cd "$PY_SRC_DIR"

# 清理、配置编译环境
sudo make clean || true
unset CFLAGS
unset CPPFLAGS
unset LDFLAGS

echo "⚙️ 配置编译参数..."
sudo ./configure --enable-optimizations --with-ensurepip=install --without-pydebug

echo "🔨 开始编译（可能需要较长时间）..."
sudo make -j$(nproc)

echo "📦 正在安装..."
sudo make altinstall

# 验证 python3.12 是否安装成功
if [ ! -f "$PY_BIN" ]; then
  echo "❌ 安装失败，未找到 $PY_BIN"
  exit 1
fi

echo "✅ Python 安装成功："
"$PY_BIN" --version

# 检查 pip
echo "📦 检查 pip 安装..."
"$PY_BIN" -m ensurepip --upgrade
"$PY_BIN" -m pip install --upgrade pip setuptools wheel

# 测试 venv 是否正常
echo "🧪 测试 venv 模块..."
TEST_VENV_DIR="/tmp/test_venv_$RANDOM"
"$PY_BIN" -m venv "$TEST_VENV_DIR"
if [ -f "$TEST_VENV_DIR/bin/activate" ]; then
  echo "✅ venv 创建成功：$TEST_VENV_DIR"
  rm -rf "$TEST_VENV_DIR"
else
  echo "⚠️ venv 创建失败"
fi

# 询问是否设置 python3 默认为 3.12
read -p "🛠️ 是否设置 python3 默认为 python$PY_SHORT？ [y/N] " set_default
if [[ "$set_default" =~ ^[Yy]$ ]]; then
  sudo update-alternatives --install /usr/bin/python3 python3 "$PY_BIN" 2
  echo "✅ 已设置 python3 为 $PY_BIN"
fi

