import asyncio
from fastmcp import FastMCP
from kaiwu_mcp.fake_control.waic_demo_mcp import mcp as waic_mcp

# factmcp dev server.py启动时要求要找到mcp对象，所以必须定义在全局
mcp = FastMCP(name="Kaiwu MCP")

# 把多个server集合起来一起启动
async def setup():
    await mcp.import_server("", waic_mcp)


if __name__ == "__main__":
    asyncio.run(setup())
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000, log_level="DEBUG")
